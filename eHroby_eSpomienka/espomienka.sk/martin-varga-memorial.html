<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Digitálna spomienka na Martina Vargu - ho<PERSON><PERSON><PERSON> vodcu, fotografa prírody a hudobníka z Liptova">
    <title>Spomienka na Martina Vargu | 1975 - 2023</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lora:wght@400;500;600&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'lora': ['Lora', 'serif'],
                    },
                    colors: {
                        'forest': {
                            50: '#f0f9f0',
                            100: '#dcf2dc',
                            200: '#bce5bc',
                            300: '#8dd18d',
                            400: '#5bb85b',
                            500: '#3a9f3a',
                            600: '#2d7f2d',
                            700: '#256525',
                            800: '#1f5120',
                            900: '#1a431b',
                        },
                        'earth': {
                            50: '#faf8f3',
                            100: '#f4f0e6',
                            200: '#e8dcc7',
                            300: '#d9c5a0',
                            400: '#c8a876',
                            500: '#b8954a',
                            600: '#a6843f',
                            700: '#8a6d35',
                            800: '#715930',
                            900: '#5c4a2a',
                        },
                        'stone': {
                            50: '#fafafa',
                            100: '#f4f4f5',
                            200: '#e4e4e7',
                            300: '#d4d4d8',
                            400: '#a1a1aa',
                            500: '#71717a',
                            600: '#52525b',
                            700: '#3f3f46',
                            800: '#27272a',
                            900: '#18181b',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        .hero-bg {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.3)), 
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }
        
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
        }
        
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .gallery-item {
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: scale(1.05);
        }
        
        .audio-player {
            background: linear-gradient(135deg, #f0f9f0, #dcf2dc);
            border: 2px solid #bce5bc;
        }
    </style>
</head>
<body class="font-inter bg-stone-50">
    <!-- Sticky Navigation -->
    <nav class="fixed top-0 w-full bg-white/90 backdrop-blur-sm shadow-sm z-50">
        <div class="max-w-6xl mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-lora font-semibold text-stone-800">Spomienka na Martina Vargu</h1>
                <div class="hidden md:flex space-x-8">
                    <a href="#zivot" class="text-stone-600 hover:text-forest-600 transition-colors">Život</a>
                    <a href="#galeria" class="text-stone-600 hover:text-forest-600 transition-colors">Galéria</a>
                    <a href="#hudba" class="text-stone-600 hover:text-forest-600 transition-colors">Hudba</a>
                    <a href="#spomienky" class="text-stone-600 hover:text-forest-600 transition-colors">Spomienky</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-bg min-h-screen flex items-center justify-center text-white">
        <div class="text-center px-6 max-w-4xl mx-auto">
            <h1 class="text-6xl md:text-8xl font-lora font-light mb-4 fade-in">Martin Varga</h1>
            <p class="text-2xl md:text-3xl font-light mb-8 fade-in">1975 – 2023</p>
            <div class="max-w-3xl mx-auto fade-in">
                <p class="text-lg md:text-xl leading-relaxed">
                    Človek, ktorý našiel svoj pokoj v tichu hôr a ktorého odkaz žije v každom štíte, 
                    ktorý zdolal, v každej piesni, ktorú zložil, a v srdciach tých, ktorých sa dotkol.
                </p>
            </div>
        </div>
    </section>

    <!-- Život Section -->
    <section id="zivot" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-6">
            <h2 class="text-4xl font-lora font-semibold text-center mb-16 text-stone-800 fade-in">Jeho Cesta</h2>
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="fade-in">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Martin Varga" 
                         class="rounded-lg shadow-lg w-full h-96 object-cover">
                </div>
                <div class="fade-in">
                    <p class="text-lg leading-relaxed text-stone-700 mb-6">
                        Martin sa narodil v Liptovskom Mikuláši a celý svoj život zasvätil horám. 
                        Ako horský vodca sprevádzal stovky ľudí po majestátnych chodníkoch Vysokých Tatier, 
                        kde sa cítil najviac doma.
                    </p>
                    <p class="text-lg leading-relaxed text-stone-700 mb-6">
                        Jeho fotoaparát bol jeho verným spoločníkom, ktorým zachytával prchavé momenty 
                        krásy prírody. Po večeroch pri krbe často bral do rúk gitaru a skladal piesne 
                        o horách, slobode a priateľstve.
                    </p>
                    <p class="text-lg leading-relaxed text-stone-700">
                        Jeho život bol tichou oslavou všetkého, čo je skutočné a trvácne.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Galéria Section -->
    <section id="galeria" class="py-20 bg-stone-100">
        <div class="max-w-6xl mx-auto px-6">
            <h2 class="text-4xl font-lora font-semibold text-center mb-16 text-stone-800 fade-in">Okamihy zachytené v čase</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="gallery-item fade-in">
                    <img src="https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Turistický batoh a kompas" 
                         class="rounded-lg shadow-md w-full h-64 object-cover">
                </div>
                <div class="gallery-item fade-in">
                    <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Akustická gitara v lese" 
                         class="rounded-lg shadow-md w-full h-64 object-cover grayscale">
                </div>
                <div class="gallery-item fade-in">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Horské pleso s odrazom" 
                         class="rounded-lg shadow-md w-full h-64 object-cover">
                </div>
                <div class="gallery-item fade-in">
                    <img src="https://images.unsplash.com/photo-1504280390367-361c6d9f38f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Oheň pod hviezdami" 
                         class="rounded-lg shadow-md w-full h-64 object-cover">
                </div>
            </div>
        </div>
    </section>

    <!-- Hudba Section -->
    <section id="hudba" class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <h2 class="text-4xl font-lora font-semibold mb-16 text-stone-800 fade-in">Tóny jeho duše</h2>
            
            <div class="audio-player rounded-lg p-8 mb-8 fade-in">
                <div class="flex items-center justify-center space-x-4 mb-4">
                    <button class="bg-forest-600 text-white rounded-full p-3 hover:bg-forest-700 transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5v10l7-5-7-5z"/>
                        </svg>
                    </button>
                    <div class="flex-1 bg-stone-300 rounded-full h-2">
                        <div class="bg-forest-600 h-2 rounded-full w-1/3"></div>
                    </div>
                    <span class="text-sm text-stone-600">2:34 / 4:12</span>
                </div>
                <p class="font-medium text-stone-800">Martin Varga - Ranná Rosa na Štrbskom Plese.mp3</p>
            </div>
            
            <blockquote class="text-xl italic text-stone-600 font-lora fade-in">
                „V každom tóne gitary je ozvena ticha, ktoré počuť len tam hore, bližšie k nebu."
            </blockquote>
        </div>
    </section>

    <!-- Spomienky Section -->
    <section id="spomienky" class="py-20 bg-earth-50">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <h2 class="text-4xl font-lora font-semibold mb-8 text-stone-800 fade-in">Zanechajte svoju spomienku</h2>
            <p class="text-lg text-stone-700 mb-8 fade-in">
                Ak máte spomienku na Martina, o ktorú by ste sa chceli podeliť s jeho rodinou a priateľmi, 
                budeme vám vďační, ak nám ju pošlete.
            </p>
            <a href="mailto:<EMAIL>" 
               class="inline-block bg-forest-600 text-white px-8 py-4 rounded-lg font-medium hover:bg-forest-700 transition-colors fade-in">
                Poslať spomienku e-mailom
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-stone-800 text-stone-300 py-8">
        <div class="max-w-6xl mx-auto px-6 text-center">
            <p>&copy; 2024 Rodina Vargová | S láskou vytvorené na pamiatku nášho Martina.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Initial fade-in for hero elements
        setTimeout(() => {
            document.querySelectorAll('.hero-bg .fade-in').forEach(el => {
                el.classList.add('visible');
            });
        }, 500);
    </script>
</body>
</html>
